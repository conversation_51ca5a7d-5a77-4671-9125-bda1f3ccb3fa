apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: springboot-backend
    version: v1.0.0
    environment: dev
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ai-spring-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-spring-backend
      app.kubernetes.io/name: ai-spring-backend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-spring-backend
        app.kubernetes.io/name: ai-spring-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: springboot-backend
        version: v1.0.0
        environment: dev
    spec:
      containers:
      - name: ai-spring-backend
        image: registry.digitalocean.com/doks-registry/ai-spring-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        # Environment Variables from ConfigMaps
        envFrom:
        - configMapRef:
            name: ai-spring-backend-env-config
        - configMapRef:
            name: ai-spring-backend-cors-config
        # Health checks
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        # Resource limits
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        # Volume mounts for configuration
        volumeMounts:
        - name: cors-config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: cors-config-volume
        configMap:
          name: ai-spring-backend-cors-config