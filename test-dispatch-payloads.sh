#!/bin/bash

# Test script to simulate GitHub repository dispatch events for Django and NestJS backends
# This script tests the complete GitOps automation pipeline

set -e

echo "🧪 Testing Django and NestJS Backend GitOps Automation"
echo "======================================================"

# Create base64 encoded secrets payload
SECRETS_JSON='{"JWT_SECRET":"test-jwt-secret","DB_PASSWORD":"test-db-password","SMTP_USER":"<EMAIL>","SMTP_PASS":"test-smtp-pass","GOOGLE_CLIENT_ID":"test-google-client-id","GOOGLE_CLIENT_SECRET":"test-google-client-secret"}'
SECRETS_ENCODED=$(echo -n "$SECRETS_JSON" | base64 -w 0)

echo "🔐 Generated secrets payload: $SECRETS_ENCODED"
echo ""

# Test 1: Django Backend with secrets
echo "🐍 Test 1: Django Backend with Custom Secrets"
echo "----------------------------------------------"

python scripts/generate-manifests-cicd.py \
  --app-name "Test Django API" \
  --project-id "test-django-api" \
  --application-type "django-backend" \
  --environment "dev" \
  --docker-image "python" \
  --docker-tag "3.11-slim" \
  --source-repo "test/django-api" \
  --source-branch "main" \
  --commit-sha "django123" \
  --secrets-encoded "$SECRETS_ENCODED" \
  --output-dir "."

echo "✅ Django backend generation completed"
echo ""

# Test 2: NestJS Backend with secrets  
echo "🟢 Test 2: NestJS Backend with Custom Secrets"
echo "----------------------------------------------"

python scripts/generate-manifests-cicd.py \
  --app-name "Test NestJS Service" \
  --project-id "test-nest-service" \
  --application-type "nest-backend" \
  --environment "staging" \
  --docker-image "node" \
  --docker-tag "18-alpine" \
  --source-repo "test/nest-service" \
  --source-branch "main" \
  --commit-sha "nest456" \
  --secrets-encoded "$SECRETS_ENCODED" \
  --output-dir "."

echo "✅ NestJS backend generation completed"
echo ""

# Test 3: Django Backend without secrets (using defaults)
echo "🐍 Test 3: Django Backend with Default Secrets"
echo "-----------------------------------------------"

python scripts/generate-manifests-cicd.py \
  --app-name "Django Default API" \
  --project-id "django-default-api" \
  --application-type "django-backend" \
  --environment "production" \
  --docker-image "python" \
  --docker-tag "3.11-slim" \
  --source-repo "test/django-default" \
  --source-branch "main" \
  --commit-sha "django789" \
  --output-dir "."

echo "✅ Django backend with defaults completed"
echo ""

# Test 4: NestJS Backend without secrets (using defaults)
echo "🟢 Test 4: NestJS Backend with Default Secrets"
echo "-----------------------------------------------"

python scripts/generate-manifests-cicd.py \
  --app-name "NestJS Default Service" \
  --project-id "nest-default-service" \
  --application-type "nest-backend" \
  --environment "production" \
  --docker-image "node" \
  --docker-tag "18-alpine" \
  --source-repo "test/nest-default" \
  --source-branch "main" \
  --commit-sha "nest789" \
  --output-dir "."

echo "✅ NestJS backend with defaults completed"
echo ""

# Verification
echo "🔍 Verification Summary"
echo "======================"

echo "Generated projects:"
ls -la | grep "^d" | grep -E "(django|nest)" | awk '{print "  - " $9}'

echo ""
echo "Checking Django API configuration..."
if [ -f "test-django-api/k8s/configmap.yaml" ]; then
    echo "  ✅ Django ConfigMap generated"
    if grep -q "DJANGO_SETTINGS_MODULE" test-django-api/k8s/configmap.yaml; then
        echo "  ✅ Django-specific configuration found"
    fi
    if grep -q "8000" test-django-api/k8s/configmap.yaml; then
        echo "  ✅ Django port 8000 configured"
    fi
fi

echo ""
echo "Checking NestJS Service configuration..."
if [ -f "test-nest-service/k8s/configmap.yaml" ]; then
    echo "  ✅ NestJS ConfigMap generated"
    if grep -q "NODE_ENV" test-nest-service/k8s/configmap.yaml; then
        echo "  ✅ NestJS-specific configuration found"
    fi
    if grep -q "3000" test-nest-service/k8s/configmap.yaml; then
        echo "  ✅ NestJS port 3000 configured"
    fi
fi

echo ""
echo "Checking secrets..."
if [ -f "test-django-api/k8s/secret.yaml" ]; then
    if grep -q "dGVzdC1qd3Qtc2VjcmV0" test-django-api/k8s/secret.yaml; then
        echo "  ✅ Custom JWT secret found in Django secrets"
    fi
fi

if [ -f "test-nest-service/k8s/secret.yaml" ]; then
    if grep -q "dGVzdC1qd3Qtc2VjcmV0" test-nest-service/k8s/secret.yaml; then
        echo "  ✅ Custom JWT secret found in NestJS secrets"
    fi
fi

echo ""
echo "🎉 All tests completed successfully!"
echo ""
echo "📋 Sample curl commands for testing dispatch events:"
echo ""

cat << 'EOF'
# Django Backend Dispatch Event
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "My Django API",
      "project_id": "my-django-api",
      "application_type": "django-backend",
      "environment": "dev",
      "docker_image": "myorg/django-api",
      "docker_tag": "v1.0.0",
      "source_repo": "myorg/django-api",
      "source_branch": "main",
      "commit_sha": "abc123",
      "secrets_encoded": "'$SECRETS_ENCODED'"
    }
  }'

# NestJS Backend Dispatch Event  
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "My NestJS Service",
      "project_id": "my-nest-service", 
      "application_type": "nest-backend",
      "environment": "dev",
      "docker_image": "myorg/nest-service",
      "docker_tag": "v2.0.0",
      "source_repo": "myorg/nest-service",
      "source_branch": "main",
      "commit_sha": "def456",
      "secrets_encoded": "'$SECRETS_ENCODED'"
    }
  }'
EOF

echo ""
echo "🔗 For more information, see: docs/django-nest-payloads.md"
