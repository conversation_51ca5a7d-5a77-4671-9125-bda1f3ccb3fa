apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-service
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: backend
    environment: dev
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ai-spring-backend
spec:
  type: LoadBalancer
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: ai-spring-backend