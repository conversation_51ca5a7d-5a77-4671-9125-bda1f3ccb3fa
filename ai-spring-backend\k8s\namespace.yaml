apiVersion: v1
kind: Namespace
metadata:
  name: ai-spring-backend-dev
  labels:
    name: ai-spring-backend-dev
    app: ai-spring-backend
    environment: dev
    managed-by: argocd
    app-type: springboot-backend
    component: backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ai-spring-backend
    app.kubernetes.io/managed-by: argocd
    # Network policy targeting labels
    network-policy.kubernetes.io/name: ai-spring-backend-dev
    cross-namespace-communication: enabled
