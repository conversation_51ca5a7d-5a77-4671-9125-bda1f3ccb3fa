#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Generate ArgoCD and Kubernetes manifests from CI/CD pipeline parameters
    
.DESCRIPTION
    This script generates complete ArgoCD applications and Kubernetes manifests
    based on parameters passed from a CI/CD pipeline, rather than GitHub issue parsing.
    It's designed to work with the repository dispatch workflow for cross-repository
    GitOps automation.
    
.PARAMETER AppName
    The human-readable application name
    
.PARAMETER ProjectId
    The project identifier (lowercase alphanumeric with hyphens)
    
.PARAMETER Environment
    Target environment (dev, staging, production)
    
.PARAMETER DockerImage
    Docker image repository (e.g., srivani8900/auth-app)
    
.PARAMETER DockerTag
    Docker image tag (e.g., latest, v1.0.0, commit-sha)
    
.PARAMETER SourceRepo
    Source repository that triggered the deployment
    
.PARAMETER SourceBranch
    Source branch that was merged
    
.PARAMETER CommitSha
    Commit SHA that triggered the deployment
    
.PARAMETER OutputDir
    Output directory for generated manifests (default: current directory)
    
.PARAMETER AppType
    Application type (default: springboot-backend)
    Supported types: react-frontend, springboot-backend, django-backend, nest-backend, web-app, api, microservice, worker, database
    
.PARAMETER ContainerPort
    Container port (default: 8080)
    
.PARAMETER Replicas
    Number of replicas (default: 1 for dev, 2 for staging/production)
    
.PARAMETER EnableDatabase
    Enable PostgreSQL database setup (default: true)
    
.PARAMETER HealthCheckPath
    Health check endpoint path (default varies by app type: / for react-frontend, /actuator/health for springboot-backend, /health/ for django-backend, /health for nest-backend)

.PARAMETER BackendType
    Backend type for react-frontend applications (spring, django, nest) - determines which backend API URL to use

.EXAMPLE
    ./generate-manifests-cicd.ps1 -AppName "My React App" -ProjectId "my-react-app" -Environment "dev" -DockerImage "myorg/react-app" -DockerTag "latest" -AppType "react-frontend" -BackendType "spring"

.EXAMPLE
    ./generate-manifests-cicd.ps1 -AppName "Auth API" -ProjectId "auth-api" -Environment "production" -DockerImage "myorg/auth-api" -DockerTag "v1.2.3" -AppType "springboot-backend" -Replicas 3
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$AppName,
    
    [Parameter(Mandatory=$true)]
    [string]$ProjectId,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "production", "feature")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$DockerImage,
    
    [Parameter(Mandatory=$true)]
    [string]$DockerTag,
    
    [Parameter(Mandatory=$false)]
    [string]$SourceRepo = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SourceBranch = "",
    
    [Parameter(Mandatory=$false)]
    [string]$CommitSha = "",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = ".",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("react-frontend", "springboot-backend", "django-backend", "nest-backend", "web-app", "api", "microservice", "worker", "database")]
    [string]$AppType = "springboot-backend",
    
    [Parameter(Mandatory=$false)]
    [int]$ContainerPort = 8080,
    
    [Parameter(Mandatory=$false)]
    [int]$Replicas = 0,
    
    [Parameter(Mandatory=$false)]
    [bool]$EnableDatabase = $true,

    [Parameter(Mandatory=$false)]
    [string]$HealthCheckPath = "/actuator/health",

    [Parameter(Mandatory=$false)]
    [ValidateSet("spring", "django", "nest", "")]
    [string]$BackendType = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-Status {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )

    switch ($Type) {
        "SUCCESS" { Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "[ERROR] $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
        "INFO" { Write-Host "[INFO] $Message" -ForegroundColor Cyan }
        default { Write-Host "$Message" }
    }
}

# Function to get API URL based on backend type for react-frontend applications
function Get-ApiUrlForBackendType {
    param(
        [string]$BackendType,
        [string]$Environment,
        [string]$ProjectId,
        [int]$ContainerPort
    )

    if ($Environment -eq "dev") {
        # External IPs for dev environment backends
        $backendUrls = @{
            'spring' = 'http://*************:8080'
            'django' = 'http://***************:8000'
            'nest' = 'http://*************:3000'
        }
        return $backendUrls[$BackendType] ?? 'http://*************:8080'  # Default to spring
    } else {
        # For staging/production, use internal service names
        $backendPorts = @{
            'spring' = '8080'
            'django' = '8000'
            'nest' = '3000'
        }
        $port = $backendPorts[$BackendType] ?? '8080'
        $backendServiceNames = @{
            'spring' = "ai-spring-backend-service:$port"
            'django' = "ai-django-backend-service:$port"
            'nest' = "ai-nest-backend-service:$port"
        }
        return "http://$($backendServiceNames[$BackendType] ?? "$ProjectId-service:$ContainerPort")"
    }
}

# Function to replace template placeholders
function Replace-TemplateVariables {
    param(
        [string]$Content,
        [hashtable]$Variables
    )
    
    $result = $Content
    foreach ($key in $Variables.Keys) {
        $placeholder = "{{$key}}"
        $value = $Variables[$key]
        $result = $result -replace [regex]::Escape($placeholder), $value
    }
    
    # Handle conditional blocks for database
    if ($Variables.ENABLE_DATABASE -eq "true") {
        $result = $result -replace '{{#if ENABLE_DATABASE}}', ''
        $result = $result -replace '{{/if}}', ''
        # Handle DB_HOST conditional logic
        $dbHost = "$($Variables.PROJECT_ID)-postgres"  # Service name within the same namespace
        $result = $result -replace '{{#if DB_HOST}}{{DB_HOST}}{{else}}[^{]*{{/if}}', $dbHost
    } else {
        # Remove database-related sections
        $result = $result -replace '(?s){{#if ENABLE_DATABASE}}.*?{{/if}}', ''
    }

    # Handle application type conditionals with a more robust approach
    $appType = $Variables.APP_TYPE
    $environment = $Variables.ENVIRONMENT

    # Split content into lines for line-by-line processing
    $lines = $result -split "`n"
    $processedLines = @()
    $skipUntilEndEq = $false
    $currentAppTypeMatch = $false

    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]

        # Check for app type conditionals
        if ($line -match '{{#eq APP_TYPE ''(.+?)''}}}') {
            $conditionAppType = $matches[1]
            if ($conditionAppType -eq $appType) {
                $currentAppTypeMatch = $true
                # Skip this conditional line, but process content inside
                continue
            } else {
                $skipUntilEndEq = $true
                # Skip this line and everything until {{/eq}}
                continue
            }
        }

        # Check for end of eq block
        if ($line -match '{{/eq}}') {
            if ($skipUntilEndEq) {
                $skipUntilEndEq = $false
            } elseif ($currentAppTypeMatch) {
                $currentAppTypeMatch = $false
            }
            # Skip the {{/eq}} line
            continue
        }

        # Skip lines if we're in a non-matching app type block
        if ($skipUntilEndEq) {
            continue
        }

        # Handle environment conditionals within the current app type
        if ($line -match '{{#eq ENVIRONMENT ''dev''}}') {
            if ($environment -eq "dev") {
                # Skip the conditional line, process content
                continue
            } else {
                # Skip until {{else}} or end of block
                $skipUntilElse = $true
                while ($i + 1 -lt $lines.Count) {
                    $i++
                    $nextLine = $lines[$i]
                    if ($nextLine -match '{{else}}') {
                        break
                    }
                }
                continue
            }
        }

        if ($line -match '{{else}}') {
            if ($environment -ne "dev") {
                # Process content after {{else}}
                continue
            } else {
                # Skip content after {{else}} until end of block
                while ($i + 1 -lt $lines.Count) {
                    $i++
                    $nextLine = $lines[$i]
                    if ($nextLine -match '{{/eq}}') {
                        $i-- # Back up one so the {{/eq}} gets processed normally
                        break
                    }
                }
                continue
            }
        }

        # Add the line to processed output
        $processedLines += $line
    }

    $result = $processedLines -join "`n"

    return $result
}

Write-Status "Starting CI/CD manifest generation..." "INFO"
Write-Status "Application: $AppName" "INFO"
Write-Status "Project ID: $ProjectId" "INFO"
Write-Status "Environment: $Environment" "INFO"
Write-Status "Docker Image: ${DockerImage}:${DockerTag}" "INFO"

# Validate inputs
if ($ProjectId -notmatch '^[a-z0-9-]+$') {
    Write-Status "Invalid project ID format: $ProjectId" "ERROR"
    Write-Status "Project ID must be lowercase alphanumeric with hyphens only" "ERROR"
    exit 1
}

# Get environment-specific configuration
Write-Status "Loading environment-specific configuration..." "INFO"
try {
    $envConfigScript = Join-Path $PSScriptRoot "environment-config.ps1"
    if (-not (Test-Path $envConfigScript)) {
        Write-Status "Environment config script not found: $envConfigScript" "WARNING"
        Write-Status "Using default configuration..." "INFO"

        # Fallback to default configuration
        if ($Replicas -eq 0) {
            $Replicas = if ($Environment -eq "dev") { 1 } elseif ($Environment -eq "staging") { 2 } else { 3 }
        }
        $memoryRequest = if ($Environment -eq "dev") { "256Mi" } elseif ($Environment -eq "staging") { "512Mi" } else { "1Gi" }
        $memoryLimit = if ($Environment -eq "dev") { "512Mi" } elseif ($Environment -eq "staging") { "1Gi" } else { "2Gi" }
        $cpuRequest = if ($Environment -eq "dev") { "100m" } elseif ($Environment -eq "staging") { "200m" } else { "500m" }
        $cpuLimit = if ($Environment -eq "dev") { "500m" } elseif ($Environment -eq "staging") { "1000m" } else { "2000m" }
        $namespace = "$ProjectId-$Environment"
        # Use external IP for dev environment to enable external connectivity
        $dbHost = if ($Environment -eq "dev") { "*************" } else { "$ProjectId-postgres" }
    } else {
        # Load environment-specific configuration
        $envConfigJson = & $envConfigScript -Environment $Environment -ProjectId $ProjectId -GetConfig
        $envConfig = $envConfigJson | ConvertFrom-Json

        # Use environment configuration
        if ($Replicas -eq 0) {
            $Replicas = $envConfig.replicas
        }
        $memoryRequest = $envConfig.resources.requests.memory
        $memoryLimit = $envConfig.resources.limits.memory
        $cpuRequest = $envConfig.resources.requests.cpu
        $cpuLimit = $envConfig.resources.limits.cpu
        $namespace = $envConfig.namespace
        $dbHost = $envConfig.database.host

        Write-Status "Environment configuration loaded successfully" "SUCCESS"
        Write-Status "Replicas: $Replicas, Memory: $memoryRequest-$memoryLimit, CPU: $cpuRequest-$cpuLimit" "INFO"
    }
} catch {
    Write-Status "Failed to load environment configuration: $_" "WARNING"
    Write-Status "Using default configuration..." "INFO"

    # Fallback to default configuration
    if ($Replicas -eq 0) {
        $Replicas = if ($Environment -eq "dev") { 1 } elseif ($Environment -eq "staging") { 2 } else { 3 }
    }
    $memoryRequest = if ($Environment -eq "dev") { "256Mi" } elseif ($Environment -eq "staging") { "512Mi" } else { "1Gi" }
    $memoryLimit = if ($Environment -eq "dev") { "512Mi" } elseif ($Environment -eq "staging") { "1Gi" } else { "2Gi" }
    $cpuRequest = if ($Environment -eq "dev") { "100m" } elseif ($Environment -eq "staging") { "200m" } else { "500m" }
    $cpuLimit = if ($Environment -eq "dev") { "500m" } elseif ($Environment -eq "staging") { "1000m" } else { "2000m" }
    $namespace = "$ProjectId-$Environment"
    # Use external IP for dev environment to enable external connectivity
    $dbHost = if ($Environment -eq "dev") { "*************" } else { "$ProjectId-postgres" }
}

# Apply application type-specific overrides
Write-Status "Applying application type-specific configuration for: $AppType" "INFO"
switch ($AppType) {
    "react-frontend" {
        # React Frontend specific defaults
        if ($ContainerPort -eq 8080) { $ContainerPort = 3000 }  # React apps typically run on port 3000
        $HealthCheckPath = "/"  # Always use / for React frontends
        $EnableDatabase = $false  # React frontends typically don't need databases

        # Lower resource requirements for React apps
        $memoryRequest = if ($Environment -eq "dev") { "128Mi" } elseif ($Environment -eq "staging") { "256Mi" } else { "512Mi" }
        $memoryLimit = if ($Environment -eq "dev") { "256Mi" } elseif ($Environment -eq "staging") { "512Mi" } else { "1Gi" }
        $cpuRequest = if ($Environment -eq "dev") { "50m" } elseif ($Environment -eq "staging") { "100m" } else { "200m" }
        $cpuLimit = if ($Environment -eq "dev") { "200m" } elseif ($Environment -eq "staging") { "500m" } else { "1000m" }

        Write-Status "React Frontend configuration applied - Port: $ContainerPort, Health: $HealthCheckPath, DB: $EnableDatabase" "INFO"
    }
    "springboot-backend" {
        # Spring Boot Backend specific defaults
        if ($ContainerPort -eq 8080) { $ContainerPort = 8080 }  # Keep default
        $HealthCheckPath = "/actuator/health"  # Always use /actuator/health for Spring Boot
        $EnableDatabase = $true  # Spring Boot backends often need databases

        # Higher resource requirements for Spring Boot apps
        $memoryRequest = if ($Environment -eq "dev") { "512Mi" } elseif ($Environment -eq "staging") { "1Gi" } else { "2Gi" }
        $memoryLimit = if ($Environment -eq "dev") { "1Gi" } elseif ($Environment -eq "staging") { "2Gi" } else { "4Gi" }
        $cpuRequest = if ($Environment -eq "dev") { "250m" } elseif ($Environment -eq "staging") { "500m" } else { "1000m" }
        $cpuLimit = if ($Environment -eq "dev") { "500m" } elseif ($Environment -eq "staging") { "1000m" } else { "2000m" }

        Write-Status "Spring Boot Backend configuration applied - Port: $ContainerPort, Health: $HealthCheckPath, DB: $EnableDatabase" "INFO"
    }
    "django-backend" {
        # Django Backend specific defaults
        if ($ContainerPort -eq 8080) { $ContainerPort = 8000 }  # Django apps typically run on port 8000
        $HealthCheckPath = "/health/"  # Django health check endpoint
        $EnableDatabase = $true  # Django backends typically need databases

        # Medium resource requirements for Django apps
        $memoryRequest = if ($Environment -eq "dev") { "256Mi" } elseif ($Environment -eq "staging") { "512Mi" } else { "1Gi" }
        $memoryLimit = if ($Environment -eq "dev") { "512Mi" } elseif ($Environment -eq "staging") { "1Gi" } else { "2Gi" }
        $cpuRequest = if ($Environment -eq "dev") { "100m" } elseif ($Environment -eq "staging") { "250m" } else { "500m" }
        $cpuLimit = if ($Environment -eq "dev") { "250m" } elseif ($Environment -eq "staging") { "500m" } else { "1000m" }

        Write-Status "Django Backend configuration applied - Port: $ContainerPort, Health: $HealthCheckPath, DB: $EnableDatabase" "INFO"
    }
    "nest-backend" {
        # NestJS Backend specific defaults
        if ($ContainerPort -eq 8080) { $ContainerPort = 3000 }  # NestJS apps typically run on port 3000
        $HealthCheckPath = "/health"  # NestJS health check endpoint
        $EnableDatabase = $true  # NestJS backends typically need databases

        # Medium resource requirements for NestJS apps
        $memoryRequest = if ($Environment -eq "dev") { "256Mi" } elseif ($Environment -eq "staging") { "512Mi" } else { "1Gi" }
        $memoryLimit = if ($Environment -eq "dev") { "512Mi" } elseif ($Environment -eq "staging") { "1Gi" } else { "2Gi" }
        $cpuRequest = if ($Environment -eq "dev") { "100m" } elseif ($Environment -eq "staging") { "250m" } else { "500m" }
        $cpuLimit = if ($Environment -eq "dev") { "250m" } elseif ($Environment -eq "staging") { "500m" } else { "1000m" }

        Write-Status "NestJS Backend configuration applied - Port: $ContainerPort, Health: $HealthCheckPath, DB: $EnableDatabase" "INFO"
    }
    default {
        # Default health check for other application types
        $HealthCheckPath = "/health"  # Default for other types
        Write-Status "Using default configuration for application type: $AppType" "INFO"
    }
}

# Get cluster configuration based on environment
function Get-ClusterConfig {
    param([string]$Environment)

    # Map environments to specific DOKS clusters
    # ArgoCD is running on cluster 158b6a47-3e7e-4dca-af0f-05a6e07115af (production)
    # Applications should be deployed to cluster 6be4e15d-52f9-431d-84ec-ec8cad0dff2d (dev/staging)
    $clusterMappings = @{
        "dev" = @{
            "server" = "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
            "name" = "doks-target-cluster"
            "cluster_id" = "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        }
        "staging" = @{
            "server" = "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
            "name" = "doks-target-cluster"
            "cluster_id" = "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        }
        "production" = @{
            "server" = "https://kubernetes.default.svc"
            "name" = "in-cluster"
            "cluster_id" = "158b6a47-3e7e-4dca-af0f-05a6e07115af"
        }
    }

    return $clusterMappings[$Environment] ?? $clusterMappings["production"]
}

# Get cluster configuration
$clusterConfig = Get-ClusterConfig -Environment $Environment
Write-Status "Cluster configuration: $($clusterConfig.name) ($($clusterConfig.cluster_id))" "INFO"

# Prepare template variables
$templateVars = @{
    PROJECT_ID = $ProjectId
    APP_NAME = $AppName
    ENVIRONMENT = $Environment
    NAMESPACE = $namespace
    APP_TYPE = $AppType
    CONTAINER_IMAGE = "$DockerImage`:$DockerTag"
    CONTAINER_PORT = $ContainerPort
    REPLICAS = $Replicas
    ENABLE_DATABASE = $EnableDatabase.ToString().ToLower()
    HEALTH_CHECK_PATH = $HealthCheckPath
    MEMORY_REQUEST = $memoryRequest
    MEMORY_LIMIT = $memoryLimit
    CPU_REQUEST = $cpuRequest
    CPU_LIMIT = $cpuLimit
    DB_USER = "postgres"
    DB_NAME = $ProjectId.Replace("-", "_")
    DB_HOST = $dbHost
    SERVICE_TYPE = "LoadBalancer"
    NODE_PORT = ""
    INGRESS_ENABLED = "false"
    INGRESS_HOST = ""
    INGRESS_PATH = "/"
    SOURCE_REPO = $SourceRepo
    SOURCE_BRANCH = $SourceBranch
    COMMIT_SHA = $CommitSha
    # Cluster configuration
    CLUSTER_SERVER = $clusterConfig.server
    CLUSTER_NAME = $clusterConfig.name
    CLUSTER_ID = $clusterConfig.cluster_id
    # Additional secret values for consistency with existing templates
    JWT_SECRET = "supersecretkey"
    DB_PASSWORD = "password"
    SMTP_USER = "<EMAIL>"
    SMTP_PASS = "fqactehafmzlltzz"
    GOOGLE_CLIENT_ID = "1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com"
    GOOGLE_CLIENT_SECRET = "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"

    # Additional template variables
    APP_VERSION = "1.0.0"
    PUBLIC_URL = "/"
    JAVA_XMS = "256m"
    JAVA_XMX = "512m"
    # API_URL configuration - use backend type for React frontends or default logic
    API_URL = if ($AppType -eq "react-frontend" -and $BackendType) {
        Get-ApiUrlForBackendType -BackendType $BackendType -Environment $Environment -ProjectId $ProjectId -ContainerPort $ContainerPort
    } elseif ($AppType -eq "react-frontend" -and $Environment -eq "dev") {
        "http://*************:8080"
    } else {
        "http://$ProjectId-service:$ContainerPort"
    }
    APP_URL = "http://$ProjectId.$Environment.local"
}

# Create project directories
$projectDir = Join-Path $OutputDir $ProjectId
$argoCdDir = Join-Path $projectDir "argocd"
$k8sDir = Join-Path $projectDir "k8s"

Write-Status "Creating project directories..." "INFO"
New-Item -ItemType Directory -Path $argoCdDir -Force | Out-Null
New-Item -ItemType Directory -Path $k8sDir -Force | Out-Null

# Generate ArgoCD and Kubernetes manifests
$templates = @(
    @{ Source = "templates/argocd/application.yaml"; Target = "$argoCdDir/application.yaml" },
    @{ Source = "templates/argocd/project.yaml"; Target = "$argoCdDir/project.yaml" },
    @{ Source = "templates/k8s/namespace.yaml"; Target = "$k8sDir/namespace.yaml" },
    @{ Source = "templates/k8s/configmap.yaml"; Target = "$k8sDir/configmap.yaml" },
    @{ Source = "templates/k8s/secret.yaml"; Target = "$k8sDir/secret.yaml" },
    @{ Source = "templates/k8s/deployment.yaml"; Target = "$k8sDir/deployment.yaml" },
    @{ Source = "templates/k8s/service.yaml"; Target = "$k8sDir/service.yaml" }
)

# Add PostgreSQL templates if database is enabled
if ($EnableDatabase) {
    Write-Status "Adding PostgreSQL templates..." "INFO"
    $templates += @(
        @{ Source = "templates/k8s/postgres-deployment.yaml"; Target = "$k8sDir/postgres-deployment.yaml" },
        @{ Source = "templates/k8s/postgres-service.yaml"; Target = "$k8sDir/postgres-service.yaml" },
        @{ Source = "templates/k8s/postgres-pvc.yaml"; Target = "$k8sDir/postgres-pvc.yaml" }
    )
} else {
    Write-Status "Skipping PostgreSQL templates (database disabled)" "INFO"
}

Write-Status "Generating manifests from templates..." "INFO"

foreach ($template in $templates) {
    $sourcePath = $template.Source
    $targetPath = $template.Target
    
    if (-not (Test-Path $sourcePath)) {
        Write-Status "Template not found: $sourcePath" "ERROR"
        exit 1
    }
    
    Write-Status "Processing: $sourcePath -> $targetPath" "INFO"
    
    try {
        $content = Get-Content $sourcePath -Raw
        $processedContent = Replace-TemplateVariables -Content $content -Variables $templateVars
        
        # Ensure target directory exists
        $targetDir = Split-Path $targetPath -Parent
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Write processed content
        $processedContent | Out-File -FilePath $targetPath -Encoding UTF8
        Write-Status "Generated: $targetPath" "SUCCESS"
    }
    catch {
        Write-Status "Failed to process template $sourcePath`: $_" "ERROR"
        exit 1
    }
}

Write-Status "Manifest generation completed successfully!" "SUCCESS"
Write-Status "Generated files in: $projectDir" "INFO"
Write-Status "ArgoCD manifests: $argoCdDir" "INFO"
Write-Status "Kubernetes manifests: $k8sDir" "INFO"

exit 0
