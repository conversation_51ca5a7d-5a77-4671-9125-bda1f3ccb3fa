apiVersion: v1
kind: Service
metadata:
  name: APP_NAME-service
  namespace: NAMESPACE
  labels:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: APP_NAME
    environment: ENVIRONMENT
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  type: SERVICE_TYPE
  ports:
  - port: CONTAINER_PORT
    targetPort: CONTAINER_PORT
    protocol: TCP
    name: http
  selector:
    app: APP_NAME
    app.kubernetes.io/name: APP_NAME
    app.kubernetes.io/managed-by: argocd

