apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-cors-config
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: backend
    config-type: cors
data:
  cors.allowed-origins: |
    http://64.225.85.157:3000
  cors.allowed-methods: "GET,POST,PUT,DELETE,OPTIONS,PATCH"
  cors.allowed-headers: |
    Accept,
    Accept-Language,
    Content-Language,
    Content-Type,
    Authorization,
    X-Requested-With,
    Origin,
    Access-Control-Request-Method,
    Access-Control-Request-Headers
  cors.allow-credentials: "true"
  cors.max-age: "3600"
  application-cors.properties: |
    cors.allowed-origins=http://64.225.85.157:3000
    cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
    cors.allowed-headers=Accept,Accept-Language,Content-Language,Content-Type,Authorization,X-Requested-With,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
    cors.allow-credentials=true
    cors.max-age=3600
    security.cors.enabled=true
    security.cors.path-pattern=/**
    logging.level.org.springframework.web.cors=DEBUG