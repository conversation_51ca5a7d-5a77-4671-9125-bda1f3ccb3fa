apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: test-backend
  namespace: argocd
  labels:
    app.kubernetes.io/name: test-backend
    app.kubernetes.io/part-of: test-backend
    environment: dev
    app-type: nest-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: test-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: test-backend/k8s
  destination:
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "nest-backend"
  - name: Configuration

